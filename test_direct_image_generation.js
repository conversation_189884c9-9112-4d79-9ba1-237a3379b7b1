// 直接测试图片生成功能
const API_BASE = 'https://storyweaver-api.stawky.workers.dev';

async function testDirectImageGeneration() {
  console.log('🎨 开始直接测试图片生成功能...');
  
  // 创建一个简单的测试请求来触发图片生成
  const testData = {
    action: 'test_image_generation',
    prompts: [
      '一只可爱的小熊在森林里探险，卡通风格，适合儿童',
      '小熊发现了一朵神奇的花，色彩鲜艳，温馨友好'
    ],
    style: 'cartoon'
  };
  
  try {
    console.log('🔧 发送图片生成测试请求...');
    
    // 发送测试请求到后端
    const response = await fetch(`${API_BASE}/api/test/image-generation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    console.log('📊 响应状态:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ 图片生成测试成功:', result);
    } else {
      const errorText = await response.text();
      console.log('⚠️ 图片生成测试响应:', errorText);
    }
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
  }
  
  // 同时测试健康检查
  console.log('\n🏥 检查API健康状态...');
  try {
    const healthResponse = await fetch(`${API_BASE}/api/health`);
    if (healthResponse.ok) {
      const health = await healthResponse.json();
      console.log('✅ API健康:', health);
    }
  } catch (error) {
    console.error('❌ 健康检查失败:', error);
  }
}

// 运行测试
testDirectImageGeneration();
